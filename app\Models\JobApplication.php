<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_id',
        'job_vacancy_id',
        'user_id',
        'status',
        'cover_letter',
        'resume_path',
        'resume_filename',
        'admin_notes',
        'reviewed_at',
        'reviewed_by',
        'is_vip_priority',
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
        'is_vip_priority' => 'boolean',
    ];

    /**
     * Vaga da candidatura
     */
    public function job()
    {
        return $this->belongsTo(JobVacancy::class, 'job_id');
    }

    /**
     * Usuário candidato
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Admin que revisou a candidatura
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope para candidaturas pendentes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'Pendente');
    }

    /**
     * Scope para candidaturas em análise
     */
    public function scopeInReview($query)
    {
        return $query->where('status', 'Em Análise');
    }

    /**
     * Scope para candidaturas aprovadas
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'Aprovada');
    }

    /**
     * Scope para candidaturas rejeitadas
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'Rejeitada');
    }

    /**
     * Scope para candidaturas VIP
     */
    public function scopeVip($query)
    {
        return $query->where('is_vip_priority', true);
    }

    /**
     * Scope para candidaturas não VIP
     */
    public function scopeNonVip($query)
    {
        return $query->where('is_vip_priority', false);
    }

    /**
     * Verificar se a candidatura foi revisada
     */
    public function isReviewed()
    {
        return !is_null($this->reviewed_at);
    }

    /**
     * Verificar se a candidatura é VIP
     */
    public function isVip()
    {
        return $this->is_vip_priority;
    }

    /**
     * Obter a classe CSS do status
     */
    public function getStatusClassAttribute()
    {
        return match ($this->status) {
            'Pendente' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
            'Em Análise' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            'Aprovada' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            'Rejeitada' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            'Contratada' => 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
            default => 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200',
        };
    }

    /**
     * Obter o nome do arquivo do currículo
     */
    public function getResumeDisplayNameAttribute()
    {
        return $this->resume_filename ?: 'Currículo.pdf';
    }

    /**
     * Verificar se tem currículo anexado
     */
    public function hasResume()
    {
        return !empty($this->resume_path);
    }

    /**
     * Verificar se tem carta de apresentação
     */
    public function hasCoverLetter()
    {
        return !empty($this->cover_letter);
    }
}
