<?php

use function Livewire\Volt\{state, computed, action};
use App\Models\Post;
use App\Models\Comment;
use App\Models\Like;
use App\Models\Notification;
use App\Models\User;
use App\Models\UserPoint;
use App\Models\UserPointLog;
use App\Services\ContentProcessor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

state([
    'limit' => 5,
    'newComment' => [],
    'showDeleteModal' => false,
    'postToDelete' => null,
    // Estados para edição de comentários
    'editingComment' => null,
    'editCommentText' => '',
    // Estados para exclusão de comentários
    'showDeleteCommentModal' => false,
    'commentToDelete' => null,
    'posts' => fn() => Post::with([
        'user.currentPhoto',
        'user.photos',
        'likedByUsers',
        'comments.user.currentPhoto',
        'comments.user.photos',
        'hashtags'
    ])
        ->when(request()->route('username'), function($query) {
            // Filtra posts do usuário correspondente ao 'username' da rota
            $username = request()->route('username');
            $user = User::where('username', $username)->first();
            if ($user) {
                return $query->where('user_id', $user->id);
            }
        })
        ->latest()
        ->take($this->limit)
        ->get()
]);


$loadMore = action(function () {
    $this->limit += 5;

    // Atualiza os posts para refletir o novo limite
    $this->posts = Post::with([
        'user.currentPhoto',
        'user.photos',
        'likedByUsers',
        'comments.user.currentPhoto',
        'comments.user.photos',
        'hashtags'
    ])
        ->latest()
        ->take($this->limit)
        ->get();


});

$toggleLike = action(function ($postId) {
    $post = Post::findOrFail($postId);
    $user = Auth::user();

    if (!$user) return;

    $wasLiked = $post->isLikedBy($user);

    if ($wasLiked) {
        // Remove curtida
        $post->likedByUsers()->detach($user->id);
        // Remove notificação
        Notification::where([
            'sender_id' => $user->id,
            'post_id' => $post->id,
            'type' => 'like'
        ])->delete();

        // Remove pontos (apenas se o post não for do próprio usuário)
        if ($post->user_id !== $user->id) {
            \App\Models\UserPoint::removePoints(
                $post->user_id,
                'like',
                5,
                "Perdeu curtida de {$user->name}",
                $post->id,
                Post::class
            );
        }
    } else {
        // Adiciona curtida
        $post->likedByUsers()->attach($user->id);

        // Adiciona pontos ao usuário que curtiu (recompensa por engajamento)
        \App\Models\UserPoint::addPoints(
            $user->id,
            'like',
            2,
            "Curtiu postagem de " . ($post->user_id === $user->id ? "sua autoria" : $post->user->name),
            $post->id,
            Post::class
        );

        // Adiciona pontos ao autor do post (se não for o mesmo usuário)
        if ($post->user_id !== $user->id) {
            \App\Models\UserPoint::addPoints(
                $post->user_id,
                'like_received',
                5,
                "Recebeu curtida de {$user->name}",
                $post->id,
                Post::class
            );
        }

        // Dispara animação de recompensa
        $this->dispatch('reward-earned', points: 2);

        // Cria notificação se não for post próprio
        if ($post->user_id !== $user->id) {
            Notification::create([
                'user_id' => $post->user_id,
                'sender_id' => $user->id,
                'type' => 'like',
                'post_id' => $post->id
            ]);
        }
    }

    // Atualiza os posts para refletir mudanças
    $this->posts = Post::with([
        'user.currentPhoto',
        'user.photos',
        'likedByUsers',
        'comments.user.currentPhoto',
        'comments.user.photos'
    ])
        ->latest()
        ->take($this->limit)
        ->get();
});

$addComment = action(function ($postId) {
    $this->validate([
        "newComment.$postId" => 'required|min:1' // Validate comment for the specific post
    ]);

    $post = Post::findOrFail($postId);
    $user = Auth::user();

    $comment = Comment::create([
        'user_id' => $user->id,
        'post_id' => $postId,
        'body' => $this->newComment[$postId]
    ]);

    // Processar menções no comentário
    ContentProcessor::processMentions($this->newComment[$postId], $comment, $user->id);

    // Adiciona pontos ao usuário que comentou
    \App\Models\UserPoint::addPoints(
        $user->id,
        'comment',
        5,
        "Comentou na postagem de " . ($post->user_id === $user->id ? "sua autoria" : $post->user->name),
        $comment->id,
        Comment::class
    );

    // Adiciona pontos ao autor do post (se não for o mesmo usuário)
    if ($post->user_id !== $user->id) {
        \App\Models\UserPoint::addPoints(
            $post->user_id,
            'comment_received',
            3,
            "Recebeu comentário de {$user->name}",
            $comment->id,
            Comment::class
        );

        // Cria notificação para o autor do post
        Notification::create([
            'user_id' => $post->user_id,
            'sender_id' => $user->id,
            'type' => 'comment',
            'post_id' => $post->id,
            'comment_id' => $comment->id
        ]);
    }

    // Dispara animação de recompensa
    $this->dispatch('reward-earned', points: 5);

    $this->newComment[$postId] = ''; // Clear the comment for the specific post
    $this->posts = Post::with([
        'user.currentPhoto',
        'user.photos',
        'likedByUsers',
        'comments.user.currentPhoto',
        'comments.user.photos',
        'hashtags'
    ])
        ->latest()
        ->take($this->limit)
        ->get();
});

$openDeleteModal = action(function ($postId) {
    $this->showDeleteModal = true;
    $this->postToDelete = $postId;
});

$closeDeleteModal = action(function () {
    $this->showDeleteModal = false;
    $this->postToDelete = null;
});

$deletePost = action(function ($postId) {
    try {
        $post = Post::findOrFail($postId);

        // Verificar se o usuário atual é o dono do post
        if (Auth::id() !== $post->user_id) {
            session()->flash('error', 'Você não tem permissão para excluir este post.');
            return;
        }

        // Excluir arquivos do storage
        if ($post->image) {
            Storage::disk('public')->delete($post->image);
        }

        if ($post->video) {
            Storage::disk('public')->delete($post->video);
        }

        // Excluir o post (as relações serão excluídas automaticamente devido às restrições de chave estrangeira)
        $post->delete();

        // Atualizar a lista de posts
        $this->posts = Post::with([
            'user.currentPhoto',
            'user.photos',
            'likedByUsers',
            'comments.user.currentPhoto',
            'comments.user.photos',
            'hashtags'
        ])
            ->latest()
            ->take($this->limit)
            ->get();

        // Fechar o modal e mostrar mensagem de sucesso
        $this->showDeleteModal = false;
        $this->postToDelete = null;
        session()->flash('message', 'Post excluído com sucesso!');
    } catch (\Exception $e) {
        session()->flash('error', 'Erro ao excluir o post: ' . $e->getMessage());
    }
});

// Métodos para edição de comentários
$startEditComment = action(function ($commentId) {
    $comment = Comment::findOrFail($commentId);

    // Verificar se o usuário pode editar este comentário
    if (!$comment->canEdit(Auth::user())) {
        session()->flash('error', 'Você não tem permissão para editar este comentário.');
        return;
    }

    $this->editingComment = $commentId;
    $this->editCommentText = $comment->body;
});

$cancelEditComment = action(function () {
    $this->editingComment = null;
    $this->editCommentText = '';
});

$updateComment = action(function ($commentId) {
    $this->validate([
        'editCommentText' => 'required|min:1|max:1000'
    ]);

    $comment = Comment::findOrFail($commentId);

    // Verificar se o usuário pode editar este comentário
    if (!$comment->canEdit(Auth::user())) {
        session()->flash('error', 'Você não tem permissão para editar este comentário.');
        return;
    }

    $comment->body = $this->editCommentText;
    $comment->markAsEdited();

    $this->editingComment = null;
    $this->editCommentText = '';

    // Atualizar posts
    $this->refreshPosts();

    session()->flash('message', 'Comentário editado com sucesso!');
});

// Métodos para exclusão de comentários
$openDeleteCommentModal = action(function ($commentId) {
    $comment = Comment::findOrFail($commentId);

    // Verificar se o usuário pode deletar este comentário
    if (!$comment->canDelete(Auth::user())) {
        session()->flash('error', 'Você não tem permissão para excluir este comentário.');
        return;
    }

    $this->showDeleteCommentModal = true;
    $this->commentToDelete = $commentId;
});

$closeDeleteCommentModal = action(function () {
    $this->showDeleteCommentModal = false;
    $this->commentToDelete = null;
});

$deleteComment = action(function ($commentId) {
    try {
        $comment = Comment::findOrFail($commentId);

        // Verificar se o usuário pode deletar este comentário
        if (!$comment->canDelete(Auth::user())) {
            session()->flash('error', 'Você não tem permissão para excluir este comentário.');
            return;
        }

        $comment->delete();

        $this->showDeleteCommentModal = false;
        $this->commentToDelete = null;

        // Atualizar posts
        $this->refreshPosts();

        session()->flash('message', 'Comentário excluído com sucesso!');
    } catch (\Exception $e) {
        session()->flash('error', 'Erro ao excluir comentário: ' . $e->getMessage());
    }
});

// Método auxiliar para atualizar posts
$refreshPosts = action(function () {
    $this->posts = Post::with([
        'user.currentPhoto',
        'user.photos',
        'likedByUsers',
        'comments.user.currentPhoto',
        'comments.user.photos',
        'hashtags'
    ])
        ->when(request()->route('username'), function($query) {
            $username = request()->route('username');
            $user = User::where('username', $username)->first();
            if ($user) {
                return $query->where('user_id', $user->id);
            }
        })
        ->latest()
        ->take($this->limit)
        ->get();
});

?>

<div x-data="{
        showImageModal: false,
        modalImageUrl: '',
        zoom: 1,
        images: [],
        currentIndex: 0,
        offsetX: 0,
        offsetY: 0,
        startX: 0,
        startY: 0,
        dragging: false,
        imgRef: null,
        openModal(url) {
            this.images = Array.from(document.querySelectorAll('[data-post-image]')).map(img => img.getAttribute('src'));
            this.currentIndex = this.images.indexOf(url);
            this.modalImageUrl = url;
            this.zoom = 1;
            this.offsetX = 0;
            this.offsetY = 0;
            this.showImageModal = true;
            this.$nextTick(() => {
                document.body.style.overflow = 'hidden';
            });
        },
        closeModal() {
            this.showImageModal = false;
            document.body.style.overflow = '';
        },
        zoomIn() { this.zoom += 0.2; },
        zoomOut() { if(this.zoom > 0.4) this.zoom -= 0.2; },
        nextImage() {
            if (this.currentIndex < this.images.length - 1) {
                this.currentIndex++;
                this.modalImageUrl = this.images[this.currentIndex];
                this.zoom = 1;
                this.offsetX = 0;
                this.offsetY = 0;
            }
        },
        prevImage() {
            if (this.currentIndex > 0) {
                this.currentIndex--;
                this.modalImageUrl = this.images[this.currentIndex];
                this.zoom = 1;
                this.offsetX = 0;
                this.offsetY = 0;
            }
        },
        handleKey(e) {
            if (!this.showImageModal) return;
            if (e.key === 'ArrowRight') this.nextImage();
            if (e.key === 'ArrowLeft') this.prevImage();
            if (e.key === 'Escape') this.closeModal();
        },
        handleWheel(e) {
            if (!this.showImageModal) return;
            e.preventDefault();
            if (e.deltaY < 0) this.zoomIn();
            else this.zoomOut();
        },
        startDrag(e) {
            if (!this.showImageModal) return;
            this.dragging = true;
            this.startX = e.clientX - this.offsetX;
            this.startY = e.clientY - this.offsetY;
        },
        onDrag(e) {
            if (!this.dragging) return;
            this.offsetX = e.clientX - this.startX;
            this.offsetY = e.clientY - this.startY;
        },
        endDrag() {
            this.dragging = false;
        }
    }"
    x-init="window.addEventListener('keydown', handleKey); $el.addEventListener('wheel', handleWheel, { passive: false });"
    >
    @foreach ($posts as $post)
        <div class="p-6 mb-6 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-md">
            <div class="flex justify-between mb-4">
                <div class="flex items-center space-x-3">
                    @php
                        $postAuthorAvatar = $post->user->currentPhoto ?? $post->user->photos->first();
                        $postAuthorAvatarUrl = $postAuthorAvatar ? Storage::url($postAuthorAvatar->photo_path) : asset('images/users/avatar.svg');
                    @endphp
                    <img src="{{ $postAuthorAvatarUrl }}"
                         class="w-10 h-10 rounded-full object-cover">
                    <div>
                        <h4 class="font-semibold text-gray-300">{{ $post->user->name }}</h4>
                        <p class="text-sm text-gray-500">
                            <a href="/{{ $post->user->username }}" class="hover:underline"> {{ '@'.$post->user->username }}</a>
                        </p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">{{ Carbon::parse($post->created_at)->diffForHumans() }}</span>

                    @if(Auth::id() === $post->user_id)
                        <button
                            wire:click="openDeleteModal({{ $post->id }})"
                            class="text-red-500 hover:text-red-700 p-1"
                            title="Excluir postagem"
                        >
                            <x-flux::icon
                                icon="trash"
                                variant="outline"
                                class="w-5 h-5"
                            />
                        </button>
                    @endif
                </div>
            </div>

            @if ($post->image)
                <img src="{{ Storage::url($post->image) }}"
                     data-post-image
                     class="w-full rounded-lg mb-4 cursor-pointer transition-transform hover:scale-105"
                     @click="openModal('{{ Storage::url($post->image) }}')"
                     alt="Imagem do post">
            @endif

            @if ($post->video)
                <video controls class="w-full rounded-lg mb-4" preload="metadata">
                    <source src="{{ Storage::url($post->video) }}" type="video/mp4">
                    Seu navegador não suporta o elemento de vídeo.
                </video>
            @endif
            <div class="text-gray-300">
                {!! ContentProcessor::processContent($post->content) !!}
            </div>

            <div class="mt-3 flex items-center space-x-2">
                <button
                    wire:click="toggleLike({{ $post->id }})"
                    class="flex items-center space-x-1 {{ $post->isLikedBy(auth()->user()) ? 'text-red-600' : 'text-gray-300' }}"
                >
                    <x-flux::icon
                        icon="heart"
                        variant="{{ $post->isLikedBy(auth()->user()) ? 'solid' : 'outline' }}"
                        class="w-5 h-5"
                    />
                    <span>{{ $post->isLikedBy(auth()->user()) ? 'Curtido' : 'Curtir' }}</span>
                </button>
                <div class="relative group">
                    <span class="text-gray-300">{{ $post->likedByUsers->count() }} Curtidas</span>

                    <!-- Tooltip com lista de usuários -->
                    @if($post->likedByUsers->count() > 0)
                        <div class="absolute bottom-full left-0 mb-2 hidden group-hover:block bg-black text-white p-2 rounded-lg shadow-lg z-50 w-48">
                            <div class="text-sm">
                                @foreach($post->likedByUsers as $user)
                                    @php
                                        $userAvatar = $user->currentPhoto ?? $user->photos->first();
                                        $userAvatarUrl = $userAvatar ? Storage::url($userAvatar->photo_path) : asset('images/users/avatar.svg');
                                    @endphp
                                    <div class="flex items-center space-x-2 mb-1">
                                        <img src="{{ $userAvatarUrl }}"
                                             class="w-6 h-6 rounded-full object-cover">
                                        <span>{{ $user->name }}</span>
                                    </div>
                                @endforeach
                            </div>
                            <div class="absolute -bottom-1 left-4 w-3 h-3 bg-black transform rotate-45"></div>
                        </div>
                    @endif
                </div>
            </div>

            <div class="mt-4 space-y-4">
                <form wire:submit="addComment({{ $post->id }})" class="flex gap-2">
                    <div x-data="mentionHashtagHandler()" class="flex-grow relative">
                        <textarea
                            wire:model="newComment.{{ $post->id }}"
                            x-ref="textarea"
                            x-on:input="handleInput($event)"
                            x-on:keydown="handleKeydown($event)"
                            class="mention-hashtag-input w-full bg-neutral-800 border border-neutral-700 rounded-lg p-3 text-gray-300 focus:outline-none focus:border-blue-500"
                            placeholder="Escreva um comentário..."
                            rows="1"
                        ></textarea>
                        <!-- Sugestões de menções -->
                        <div
                            x-show="showMentions"
                            x-transition
                            :style="mentionStyle"
                            class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto"
                        >
                            <template x-for="user in mentionSuggestions" :key="user.id">
                                <div
                                    @click="selectMention(user.username)"
                                    class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                                >
                                    <img :src="user.avatarUrl" :alt="user.username" class="w-6 h-6 rounded-full mr-2">
                                    <span x-text="user.name + ' (@' + user.username + ')'"></span>
                                </div>
                            </template>
                        </div>

                        <!-- Sugestões de hashtags -->
                        <div
                            x-show="showHashtags"
                            x-transition
                            :style="hashtagStyle"
                            class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto"
                        >
                            <template x-for="hashtag in hashtagSuggestions" :key="hashtag.id">
                                <div
                                    @click="selectHashtag(hashtag.name)"
                                    class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                                >
                                    <span x-text="'#' + hashtag.name"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                    <button type="submit" class="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        <x-flux::icon
                            icon="chat-bubble-left"
                            variant="solid"
                            class="w-4 h-4"
                        />
                        <span>Comentar</span>
                    </button>
                </form>

                <!-- Lista de comentários -->
                @foreach($post->comments as $comment)
                    @php
                        $commentAuthorAvatar = $comment->user->currentPhoto ?? $comment->user->photos->first();
                        $commentAuthorAvatarUrl = $commentAuthorAvatar ? Storage::url($commentAuthorAvatar->photo_path) : asset('images/users/avatar.svg');
                    @endphp
                    <div class="flex items-start space-x-3 p-3 bg-zinc-700 rounded-lg hover:bg-zinc-600 transition border-neutral-200 dark:border-neutral-700">
                        <img src="{{ $commentAuthorAvatarUrl }}"
                             class="w-8 h-8 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <p class="font-semibold text-gray-300">
                                            <a href="/{{ $comment->user->username }}" class="hover:underline">
                                                {{ $comment->user->username }}
                                            </a>
                                        </p>
                                        @if($comment->isEdited())
                                            <span class="text-xs text-gray-500 italic">(editado)</span>
                                        @endif
                                        <span class="text-xs text-gray-500">
                                            {{ $comment->created_at->diffForHumans() }}
                                        </span>
                                    </div>

                                    @if($editingComment === $comment->id)
                                        <!-- Formulário de edição -->
                                        <form wire:submit="updateComment({{ $comment->id }})" class="mt-2">
                                            <div class="flex gap-2">
                                                <input
                                                    wire:model="editCommentText"
                                                    type="text"
                                                    class="flex-1 p-2 border border-neutral-200 dark:border-neutral-700 rounded-lg text-gray-300 bg-zinc-600"
                                                    placeholder="Editar comentário..."
                                                    autofocus
                                                >
                                                <button type="submit" class="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm">
                                                    <x-flux::icon icon="check" class="w-4 h-4" />
                                                </button>
                                                <button type="button" wire:click="cancelEditComment" class="px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm">
                                                    <x-flux::icon icon="x-mark" class="w-4 h-4" />
                                                </button>
                                            </div>
                                        </form>
                                    @else
                                        <!-- Texto do comentário -->
                                        <div class="text-gray-100 mt-1">
                                            {!! ContentProcessor::processContent($comment->body) !!}
                                        </div>
                                    @endif
                                </div>

                                <!-- Botões de ação (apenas para o autor do comentário) -->
                                @if($comment->canEdit(Auth::user()) && $editingComment !== $comment->id)
                                    <div class="flex items-center space-x-1 ml-2">
                                        <button
                                            wire:click="startEditComment({{ $comment->id }})"
                                            class="text-gray-400 hover:text-blue-400 p-1"
                                            title="Editar comentário"
                                        >
                                            <x-flux::icon icon="pencil" class="w-4 h-4" />
                                        </button>

                                        @if($comment->canDelete(Auth::user()))
                                            <button
                                                wire:click="openDeleteCommentModal({{ $comment->id }})"
                                                class="text-gray-400 hover:text-red-400 p-1"
                                                title="Excluir comentário"
                                            >
                                                <x-flux::icon icon="trash" class="w-4 h-4" />
                                            </button>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach

    <!-- Modal de imagem ampliada -->
    <div x-show="showImageModal" x-transition class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80" style="display: none;">
        <div class="relative flex items-center justify-center w-full h-full select-none">
            <!-- Botão fechar -->
            <button @click="closeModal" class="absolute top-4 right-4 bg-gray-800 bg-opacity-70 rounded-full p-2 text-white hover:bg-opacity-100 z-20">
                <svg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'/></svg>
            </button>
            <!-- Botão anterior -->
            <button @click="prevImage" :disabled="currentIndex === 0" class="absolute left-2 md:left-8 top-1/2 -translate-y-1/2 bg-gray-800 bg-opacity-70 rounded-full p-2 text-white hover:bg-opacity-100 z-20 disabled:opacity-50">
                <svg xmlns='http://www.w3.org/2000/svg' class='h-8 w-8' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 19l-7-7 7-7'/></svg>
            </button>
            <!-- Botão próximo -->
            <button @click="nextImage" :disabled="currentIndex === images.length - 1" class="absolute right-2 md:right-8 top-1/2 -translate-y-1/2 bg-gray-800 bg-opacity-70 rounded-full p-2 text-white hover:bg-opacity-100 z-20 disabled:opacity-50">
                <svg xmlns='http://www.w3.org/2000/svg' class='h-8 w-8' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'/></svg>
            </button>
            <!-- Imagem -->
            <div class="flex flex-col items-center w-full">
                <img :src="modalImageUrl"
                     :style="'transform: translate(' + offsetX + 'px,' + offsetY + 'px) scale(' + zoom + '); max-width:90vw; max-height:80vh; cursor: grab; user-select: none;'
                     "
                     class="rounded-lg shadow-lg transition-transform duration-200 z-10 select-none"
                     alt="Imagem ampliada"
                     x-ref="imgRef"
                     @mousedown="startDrag($event)"
                     @mousemove.window="onDrag($event)"
                     @mouseup.window="endDrag()"
                     @mouseleave.window="endDrag()"
                >
                <!-- Controles de zoom -->
                <div class="flex space-x-2 mt-4 z-20">
                    <button @click="zoomOut" class="px-3 py-1 bg-gray-700 text-white rounded hover:bg-gray-600">-</button>
                    <button @click="zoomIn" class="px-3 py-1 bg-gray-700 text-white rounded hover:bg-gray-600">+</button>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 flex items-center space-x-2">
        <flux:button wire:click="loadMore" >
            Carregar mais postagens
        </flux:button>
    </div>

    <!-- Mensagem de sucesso ou erro -->
    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 mt-4">
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4 mt-4">
            {{ session('error') }}
        </div>
    @endif

    <!-- Modal de confirmação de exclusão -->
    @if($showDeleteModal)
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg max-w-md w-full p-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Tem certeza que deseja excluir esta postagem?
            </h2>

            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Esta ação não pode ser desfeita. Todos os comentários e curtidas relacionados a esta postagem também serão excluídos.
            </p>

            <div class="flex justify-end space-x-3">
                <flux:button
                    wire:click="closeDeleteModal"
                    variant="ghost"
                >
                    <x-flux::icon icon="x-mark" class="w-4 h-4 mr-1" />
                    Cancelar
                </flux:button>

                <flux:button
                    wire:click="deletePost({{ $postToDelete }})"
                    variant="danger"
                >
                    <x-flux::icon icon="trash" class="w-4 h-4 mr-1" />
                    Excluir
                </flux:button>
            </div>
        </div>
    </div>
    @endif

    <!-- Modal de confirmação de exclusão de comentário -->
    @if($showDeleteCommentModal)
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg max-w-md w-full p-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Tem certeza que deseja excluir este comentário?
            </h2>

            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Esta ação não pode ser desfeita. O comentário será removido permanentemente.
            </p>

            <div class="flex justify-end space-x-3">
                <flux:button
                    wire:click="closeDeleteCommentModal"
                    variant="ghost"
                >
                    <x-flux::icon icon="x-mark" class="w-4 h-4 mr-1" />
                    Cancelar
                </flux:button>

                <flux:button
                    wire:click="deleteComment({{ $commentToDelete }})"
                    variant="danger"
                >
                    <x-flux::icon icon="trash" class="w-4 h-4 mr-1" />
                    Excluir
                </flux:button>
            </div>
        </div>
    </div>
    @endif
</div>
