<?php

namespace App\Http\Controllers;

use App\Models\JobVacancy;
use App\Models\JobApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class JobApplicationController extends Controller
{
    /**
     * Aplicar para uma vaga
     */
    public function store(Request $request, $jobId)
    {
        // Verificar se o usuário está autenticado
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Você precisa estar logado para se candidatar.');
        }

        $job = JobVacancy::active()->findOrFail($jobId);

        // Verificar se a vaga ainda está aceitando candidaturas
        if (!$job->isAcceptingApplications()) {
            return back()->with('error', 'Esta vaga não está mais aceitando candidaturas.');
        }

        // Verificar se o usuário já se candidatou
        if ($job->hasUserApplied(Auth::id())) {
            return back()->with('error', 'Você já se candidatou para esta vaga.');
        }

        // Validação
        $rules = [];
        
        if ($job->requires_resume) {
            $rules['resume'] = 'required|file|mimes:pdf,doc,docx|max:2048';
        }
        
        if ($job->requires_cover_letter) {
            $rules['cover_letter'] = 'required|string|min:100|max:2000';
        } else {
            $rules['cover_letter'] = 'nullable|string|max:2000';
        }

        $validator = Validator::make($request->all(), $rules, [
            'resume.required' => 'O currículo é obrigatório para esta vaga.',
            'resume.file' => 'O arquivo deve ser válido.',
            'resume.mimes' => 'O currículo deve ser um arquivo PDF, DOC ou DOCX.',
            'resume.max' => 'O currículo não pode ter mais de 2MB.',
            'cover_letter.required' => 'A carta de apresentação é obrigatória para esta vaga.',
            'cover_letter.min' => 'A carta de apresentação deve ter pelo menos 100 caracteres.',
            'cover_letter.max' => 'A carta de apresentação não pode ter mais de 2000 caracteres.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Processar upload do currículo
        $resumePath = null;
        $resumeFilename = null;
        
        if ($request->hasFile('resume')) {
            $file = $request->file('resume');
            $filename = 'resume_' . Auth::id() . '_' . time() . '.' . $file->getClientOriginalExtension();
            $resumePath = $file->storeAs('job-applications', $filename, 'private');
            $resumeFilename = $file->getClientOriginalName();
        }

        // Criar candidatura
        $application = JobApplication::create([
            'job_id' => $job->id,
            'user_id' => Auth::id(),
            'cover_letter' => $request->cover_letter,
            'resume_path' => $resumePath,
            'resume_filename' => $resumeFilename,
            'is_vip_priority' => Auth::user()->role === 'vip',
        ]);

        // Incrementar contador de candidaturas da vaga
        $job->increment('applications_count');

        return back()->with('success', 'Candidatura enviada com sucesso! ' . 
            (Auth::user()->role === 'vip' ? 'Sua candidatura VIP terá prioridade na análise.' : ''));
    }

    /**
     * Exibir candidatura do usuário
     */
    public function show($applicationId)
    {
        $application = JobApplication::with(['job', 'user'])
            ->where('user_id', Auth::id())
            ->findOrFail($applicationId);

        return view('job-applications.show', compact('application'));
    }

    /**
     * Cancelar candidatura
     */
    public function destroy($applicationId)
    {
        $application = JobApplication::where('user_id', Auth::id())
            ->findOrFail($applicationId);

        // Verificar se pode cancelar (apenas se ainda estiver pendente)
        if ($application->status !== 'Pendente') {
            return back()->with('error', 'Não é possível cancelar uma candidatura que já foi analisada.');
        }

        // Remover arquivo do currículo se existir
        if ($application->resume_path) {
            Storage::disk('private')->delete($application->resume_path);
        }

        // Decrementar contador de candidaturas da vaga
        $application->job->decrement('applications_count');

        $application->delete();

        return back()->with('success', 'Candidatura cancelada com sucesso.');
    }

    /**
     * Download do currículo (apenas para admin ou próprio usuário)
     */
    public function downloadResume($applicationId)
    {
        $application = JobApplication::with('user')->findOrFail($applicationId);

        // Verificar permissão
        if (!Auth::user()->isAdmin() && Auth::id() !== $application->user_id) {
            abort(403, 'Acesso negado.');
        }

        if (!$application->resume_path) {
            abort(404, 'Currículo não encontrado.');
        }

        $path = Storage::disk('private')->path($application->resume_path);
        
        return response()->download($path, $application->resume_filename);
    }

    /**
     * Listar candidaturas do usuário
     */
    public function myApplications()
    {
        $applications = JobApplication::with(['job.category'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('job-applications.my-applications', compact('applications'));
    }
}
