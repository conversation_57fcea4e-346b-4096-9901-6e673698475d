<form wire:submit.prevent="apply" class="space-y-4">
    @csrf
    <div class="mb-2">
        <label class="block font-semibold mb-1"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PDF, DOC, DOCX) <span class="text-red-500">*</span></label>
        <input type="file" wire:model="resume" accept=".pdf,.doc,.docx" class="flux:input" required />
        @error('resume') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        <div wire:loading wire:target="resume" class="text-xs text-zinc-500 mt-1">Enviando arquivo...</div>
    </div>
    @if($job->requires_cover_letter)
    <div class="mb-2">
        <label class="block font-semibold mb-1">Carta de Apresentação <span class="text-red-500">*</span></label>
        <textarea wire:model.defer="cover_letter" class="flux:input" rows="4" required></textarea>
        @error('cover_letter') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
    </div>
    @endif
    <div class="mb-2">
        <span class="text-xs text-blue-700 dark:text-blue-300 flex items-center gap-1">
            <x-flux:icon name="star" class="w-4 h-4 text-yellow-400" />
            Usuários VIP têm prioridade na análise!
        </span>
    </div>
    <div class="flex gap-2 mt-4">
        <button type="submit" class="flux:button flux:button-primary">Enviar Candidatura</button>
    </div>
    @if (session('success'))
        <div class="mt-2 text-green-600">{{ session('success') }}</div>
    @endif
    @if (session('error'))
        <div class="mt-2 text-red-600">{{ session('error') }}</div>
    @endif
</form>
