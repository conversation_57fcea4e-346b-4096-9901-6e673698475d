# desiree
Website Desiree club

## Descrição do Frontend
O site do Desiree Swing Club apresenta uma interface moderna e responsiva, com as seguintes seções principais:

### Navegação
- Menu com links para diferentes seções da página.
- Botões para login, registro e acesso à área VIP.

### Seção Hero
- Destaque para eventos especiais com informações de data e localização.
- Botões de ação e vídeo de fundo.
- Opções de compartilhamento social.

### Sobre a Desiree
- Descrição do clube e sua proposta.
- Imagem ilustrativa e informações sobre a infraestrutura.

### Atrações
- Destaque para artistas e atrações com informações detalhadas.

### Programação
- Tabela com a programação semanal de eventos.

### Assinatura VIP
- Planos de assinatura com benefícios exclusivos.
- Opções de planos mensais e semestrais.

### Reserva
- Promoções específicas para reservas, como aniversários e eventos.

### Perguntas Frequentes (FAQ)
- Seção de perguntas frequentes com formulário para envio de dúvidas.

### Estrutura
- Descrição detalhada dos ambientes do clube, divididos por pisos.

### Contato
- Formulário de contato e mapa de localização.

### Rodapé
- Links para redes sociais, menu adicional e informações de contato.

## Funcionalidades do Projeto
Atualmente, o projeto conta com as seguintes funcionalidades:

1. **Gerenciamento de Usuários**:
   - Autenticação e gerenciamento de perfis.
   - Suporte para fotos de perfil e capa.

2. **Gerenciamento de Conteúdo**:
   - Criação e gerenciamento de posts e comentários.
   - Organização de conteúdo por categorias.

3. **Recursos Sociais**:
   - Solicitações de amizade e notificações.
   - Funcionalidade de curtidas em posts.

4. **Dados Geográficos**:
   - Integração de cidades e estados para recursos baseados em localização.

5. **Componentes de UI**:
   - Componentes Blade personalizados para elementos de interface.
   - Suporte a temas e design responsivo.

6. **Recursos Backend**:
   - Migrações e seeders para gerenciamento estruturado de dados.
   - Factories para testes e geração de dados.

7. **Testes**:
   - Testes unitários e de funcionalidade usando Pest e PHPUnit.

8. **Ferramentas de Build**:
   - Vite para empacotamento de ativos e desenvolvimento.

## Checklist para Controller e Views
- Verifique se o controller está no namespace correto.
  - filepath: `c:\xampp\htdocs\desiree2\app\Http\Controllers\JobVacancyController.php`
- Verifique se as views existem.
  - filepath: `c:\xampp\htdocs\desiree2\resources\views\job_vacancies\index.blade.php`
  - filepath: `c:\xampp\htdocs\desiree2\resources\views\job_vacancies\show.blade.php`
- Limpe o cache de rotas.
  - Execute no terminal: `php artisan route:clear`
- Verifique se não há outro arquivo de rotas sobrescrevendo /vagas.
  - filepath: `c:\xampp\htdocs\desiree2\routes\web.php`
